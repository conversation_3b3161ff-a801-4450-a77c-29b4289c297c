package middlewares

import (
	"context"
	"os"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// AuthGuard é um middleware responsável pela dupla verificação do JWT.
func AuthGuard() echo.MiddlewareFunc {
	return middleware.JWTWithConfig(middleware.JWTConfig{
		SigningKey: []byte(os.Getenv("API_ACCESS_JWT_KEY")),
	})
}

func AuthSameUserGuard(service user.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			paramUserId := c.Param("id")
			userToken, err := token.GetClaimsFromRequest(c.Request())
			if err != nil {
				return err
			}

			if isAdminUser, _ := isAdmin(c.Request().Context(), service, userToken.Uid); isAdminUser {
				return next(c)
			}

			if paramUserId != userToken.Uid {
				return errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
			}

			return next(c)
		}
	}
}

func isAdmin(ctx context.Context, service user.Service, uid string) (bool, error) {
	user, err := service.Find(ctx, uid)
	if err != nil {
		return false, err
	}
	if user.Roles == nil || len(user.Roles) <= 0 {
		return false, errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
	}
	for _, role := range user.Roles {
		if role == "admin" {
			return true, nil
		}
	}

	return false, errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
}

func AdminGuard() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			claims, err := token.GetClaimsFromRequest(c.Request())
			if err != nil {
				return err
			}

			if claims.Role != "admin" {
				return errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
			}

			return next(c)
		}
	}
}

// ServiceAuthMiddleware validates service-to-service authentication using a token
// from the Authorization header. The token should be in the format "Bearer TOKEN"
// and must match the MAIN_BACKEND_SERVICE_TOKEN environment variable.
func ServiceAuthMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get the Authorization header
			authHeader := c.Request().Header.Get("Authorization")

			// Check if the header exists and has the correct format
			if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
				return errors.New(errors.Middleware, "invalid authorization header", errors.Unauthorized, nil)
			}

			// Extract the token
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 {
				return errors.New(errors.Middleware, "invalid authorization format", errors.Unauthorized, nil)
			}

			token := parts[1]
			expectedToken := os.Getenv("MAIN_BACKEND_SERVICE_TOKEN")

			// Validate the token
			if token != expectedToken || expectedToken == "" {
				return errors.New(errors.Middleware, "invalid service token", errors.Unauthorized, nil)
			}

			// Token is valid, proceed
			return next(c)
		}
	}
}
