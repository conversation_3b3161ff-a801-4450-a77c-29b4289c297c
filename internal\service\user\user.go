package user

import (
	"context"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	_progression "github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Hard coding my area this is the number of features currently the user has access to
const AVAILABLE_FEATURES = 4

// CRUD
func (s *service) Create(ctx context.Context, user *model.User, referralCode string) error {
	foundUser, err := s.Repository.FindByEmail(ctx, user.Email)
	if err != nil {
		// If it's a "not found" error, continue with user creation
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			// Stop creation for any other type of error (internal errors, etc.), propagate it.
			return err
		}
	} else if foundUser != nil {
		return errors.New(errors.Service, errors.UserConflictExists, errors.Conflict, nil)
	}

	// Handle referral code
	var referee *model.User
	if referralCode != "" {
		referee, err = s.Repository.FindByReferral(ctx, referralCode)
		if err != nil {
			return err
		}

		// Update user with referral code and referring user ID
		user.UsedReferralCode = referralCode
		user.ReferringUserID = referee.ID
	}

	// Start user onboarding
	if err := s.onboarding(user); err != nil {
		return err
	}

	userID, err := s.Repository.Create(ctx, user)
	if err != nil {
		return err
	}

	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return err
	}

	user.ID = userID
	user.ObjectID = userObjectID

	// Propagate error. It should be treaten in the Initialization of each service.
	if err := s.ProgressionService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.VaultService.Initialize(ctx, userID, referee != nil); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.ContractService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.DreamboardService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}

	if err := s.FinancialDNAService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}

	if err := s.FinancialSheetService.Initialize(ctx, userID, user.Name); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
	}

	// Update referee vault if referral code is used.
	if referralCode != "" && referee != nil {
		refereeVault, err := s.VaultService.FindByUser(ctx, referee.ID)
		if err != nil {
			return err
		}
		refereeVault.Coins = refereeVault.Coins + 10

		err = s.VaultService.Update(ctx, refereeVault)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*model.User, error) {
	return s.findUser(ctx, func(ctx context.Context) (*model.User, error) {
		userObjectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, errors.New(errors.Service, errors.UserInvalidID, errors.Validation, err)
		}
		return s.Repository.Find(ctx, userObjectID)
	})
}

func (s *service) FindAll(ctx context.Context) ([]*model.User, error) {
	users, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *service) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	user, err := s.Repository.FindByReferral(ctx, referralCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// External Integration - Stripe
func (s *service) FindByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	user, err := s.Repository.FindByExternalCode(ctx, externalCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByExternalCode(ctx, externalCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// External Integration - Kiwify
func (s *service) FindByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	user, err := s.Repository.FindByExternalCodeKiwify(ctx, externalCodeKiwify)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByExternalCodeKiwify(ctx, externalCodeKiwify)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Update performs a full update with validation
func (s *service) Update(ctx context.Context, user *model.User) error {
	if err := user.Validate(); err != nil {
		return err
	}

	if user.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(user.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid user ID", errors.Validation, err)
		}
		user.ObjectID = objID
	}

	// Since user role manipulation is not allowed, we need to fetch the user from the database to get the current roles
	foundUser, err := s.Repository.Find(ctx, user.ObjectID)
	if err != nil {
		return err
	}
	user.Roles = foundUser.Roles

	return s.Repository.Update(ctx, user)
}

// Patch performs a partial update while preserving protected fields
func (s *service) Patch(ctx context.Context, user *model.User, patchData *model.User) error {
	// Keep existing roles since they cannot be modified via patch
	existingRoles := user.Roles

	// Prepare update with patch data
	if err := user.PrepareUpdate(patchData); err != nil {
		return err
	}

	// Restore roles to prevent modification
	user.Roles = existingRoles

	// Apply update without validation since it's a partial update
	return s.Repository.Update(ctx, user)
}

func (s *service) Sync(ctx context.Context, user *model.User) error {
	return s.syncCustomer(ctx, user)
}

func (s *service) Delete(ctx context.Context, id string, reason *model.DeleteReason) error {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return err
	}

	// Try to delete the customer from stipe if it has external code
	if foundUser.ExternalCode != "" {
		if err := s.CustomerService.Delete(foundUser.ExternalCode); err != nil {
			return err
		}
	}

	deletedUser := model.DeletedUser{
		User:         foundUser,
		DeleteReason: reason,
	}

	if err = s.Repository.CreateDelete(ctx, &deletedUser); err != nil {
		return err
	}

	err = s.Repository.Delete(ctx, foundUser.ObjectID)
	if err != nil {
		return err
	}
	return nil
}

// Card CRUD
func (s *service) FindCard(ctx context.Context, userId string) (*model.UserCard, error) {
	// Updated call to receive walletID string instead of wallet slice
	foundUser, foundProgressionTrophies, foundVault, foundWalletID, err := s.getUserCardData(ctx, userId)
	if err != nil {
		return nil, err
	}

	userFirstName := strings.Split(foundUser.Name, " ")[0]

	// Check if foundProgressionTrophies is nil and assign an empty slice if it is
	if foundProgressionTrophies == nil {
		foundProgressionTrophies = []*_progression.Trophy{}
	}

	userCard := &model.UserCard{
		ID:                foundUser.ID,
		Name:              userFirstName,
		Email:             foundUser.Email,
		Avatar:            foundUser.PhotoURL,
		Coins:             foundVault.Coins,
		Diamonds:          foundVault.Diamonds,
		ReferralCode:      foundUser.ReferralCode,
		Trophies:          int64(len(foundProgressionTrophies)),
		AvailableFeatures: AVAILABLE_FEATURES,
		// Wallet and Investments are set below based on foundWalletID
	}

	// Set Wallet ID if found, removed Investments count as it's not directly available
	if foundWalletID != "" {
		userCard.Wallet = foundWalletID
		// userCard.Investments = ... // Removed as per plan
	}

	return userCard, nil
}

// Utility
func (s *service) OnlyAdmin(ctx context.Context, id string) error {
	admins, err := s.Repository.FindAdmins(ctx)
	if err != nil {
		return err
	}

	if admins == nil || len(admins) <= 0 {
		return errors.New(errors.Service, errors.AdminUsersNotFound, errors.NotFound, nil)
	}

	onlyAdmin := true
	for _, admin := range admins {
		if admin.ID != id {
			onlyAdmin = false
		}
	}

	if onlyAdmin {
		return errors.New(errors.Service, errors.AdminUsersNotFound, errors.NotFound, nil)
	}

	return nil
}

// Helper

func (s *service) findUser(ctx context.Context, findFn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	user, err := findFn(ctx)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) syncCustomer(ctx context.Context, user *model.User) error {
	if user.ExternalCode == "" {
		createdCustomer, err := s.CustomerService.Create(user)
		if err != nil {
			return err
		}

		user.ExternalCode = createdCustomer.ID
		err = s.Update(ctx, user)
		if err != nil {
			return err
		}
		return nil
	}
	_, err := s.CustomerService.Update(user)
	if err != nil {
		return err
	}

	return nil
}

// Helper
// Updated return signature to return wallet ID string instead of wallet slice
func (s *service) getUserCardData(ctx context.Context, id string) (*model.User, []*_progression.Trophy, *model.Vault, string, error) {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return nil, nil, nil, "", err
	}

	foundProgressionTrophies, err := s.ProgressionService.FindAllTrophies(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for trophies gracefully if needed, otherwise propagate
		return nil, nil, nil, "", err
	}

	foundVault, err := s.VaultService.FindByUser(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for vault gracefully if needed, otherwise propagate
		return nil, nil, nil, "", err
	}

	// Replaced WalletService.FindByUser with ContractService.FindFirstWalletIDByCustomer
	foundWalletID, err := s.ContractService.FindFirstWalletIDByCustomer(ctx, foundUser.ID)
	if err != nil {
		// Propagate error from FindFirstWalletIDByCustomer
		return nil, nil, nil, "", err
	}

	// Return wallet ID string
	return foundUser, foundProgressionTrophies, foundVault, foundWalletID, nil
}

func (s *service) onboarding(user *model.User) error {
	if err := user.Onboarding.AgeRange.IsValid(); err != nil {
		return err
	}
	user.Onboarding.AgeRange.AddLabel()

	if err := user.Onboarding.FinancialSituation.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialSituation.AddLabel()

	if err := user.Onboarding.FinancialGoal.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialGoal.AddLabel()

	for i, personalInterest := range user.Onboarding.PersonalInterests {
		if err := personalInterest.IsValid(); err != nil {
			return err
		}
		user.Onboarding.PersonalInterests[i].AddLabel()
	}

	user.Onboarding.CompletedAt = time.Now()

	return nil
}
