package migrations

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/migrator"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type MigrateGroupsToRolesMigration struct {
	db *mongo.Database
}

func NewMigrateGroupsToRolesMigration(db *mongo.Database) migrator.Migration {
	return &MigrateGroupsToRolesMigration{
		db: db,
	}
}

func (m *MigrateGroupsToRolesMigration) Name() string {
	return "migrate_groups_to_roles"
}

func (m *MigrateGroupsToRolesMigration) Up(ctx context.Context) error {
	log.Printf("Starting migration: %s", m.Name())
	
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	
	// Find all users that have the old 'groups' field
	cursor, err := userCollection.Find(ctx, bson.D{
		{Key: "groups", Value: bson.D{{Key: "$exists", Value: true}}},
	})
	if err != nil {
		return fmt.Errorf("failed to find users with groups field: %w", err)
	}
	defer cursor.Close(ctx)

	var processedCount int64
	var errorCount int64

	for cursor.Next(ctx) {
		var user bson.M
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user document: %v", err)
			errorCount++
			continue
		}

		userID := user["_id"].(primitive.ObjectID)
		
		// Extract roles from groups
		var roles []string
		if groupsInterface, exists := user["groups"]; exists {
			if groups, ok := groupsInterface.(primitive.A); ok {
				for _, groupInterface := range groups {
					if group, ok := groupInterface.(bson.M); ok {
						if identifier, exists := group["identifier"]; exists {
							if identifierStr, ok := identifier.(string); ok {
								roles = append(roles, identifierStr)
							}
						}
					}
				}
			}
		}

		// Update the user document
		updateDoc := bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "roles", Value: roles},
			}},
			{Key: "$unset", Value: bson.D{
				{Key: "groups", Value: ""},
			}},
		}

		result, err := userCollection.UpdateOne(ctx, 
			bson.D{{Key: "_id", Value: userID}}, 
			updateDoc,
		)
		if err != nil {
			log.Printf("Error updating user %s: %v", userID.Hex(), err)
			errorCount++
			continue
		}

		if result.ModifiedCount > 0 {
			processedCount++
			log.Printf("Migrated user %s: roles=%v", userID.Hex(), roles)
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error during migration: %w", err)
	}

	log.Printf("Migration completed: %s - Processed: %d, Errors: %d", m.Name(), processedCount, errorCount)
	
	if errorCount > 0 {
		return fmt.Errorf("migration completed with %d errors", errorCount)
	}

	return nil
}

func (m *MigrateGroupsToRolesMigration) Down(ctx context.Context) error {
	log.Printf("Starting rollback: %s", m.Name())
	
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	
	// Find all users that have the new 'roles' field
	cursor, err := userCollection.Find(ctx, bson.D{
		{Key: "roles", Value: bson.D{{Key: "$exists", Value: true}}},
	})
	if err != nil {
		return fmt.Errorf("failed to find users with roles field: %w", err)
	}
	defer cursor.Close(ctx)

	var processedCount int64
	var errorCount int64

	for cursor.Next(ctx) {
		var user bson.M
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user document: %v", err)
			errorCount++
			continue
		}

		userID := user["_id"].(primitive.ObjectID)
		
		// Convert roles back to groups format
		var groups []bson.M
		if rolesInterface, exists := user["roles"]; exists {
			if roles, ok := rolesInterface.(primitive.A); ok {
				for _, roleInterface := range roles {
					if role, ok := roleInterface.(string); ok {
						groups = append(groups, bson.M{
							"name":       role,
							"identifier": role,
						})
					}
				}
			}
		}

		// Update the user document
		updateDoc := bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "groups", Value: groups},
			}},
			{Key: "$unset", Value: bson.D{
				{Key: "roles", Value: ""},
			}},
		}

		result, err := userCollection.UpdateOne(ctx, 
			bson.D{{Key: "_id", Value: userID}}, 
			updateDoc,
		)
		if err != nil {
			log.Printf("Error rolling back user %s: %v", userID.Hex(), err)
			errorCount++
			continue
		}

		if result.ModifiedCount > 0 {
			processedCount++
			log.Printf("Rolled back user %s: groups=%v", userID.Hex(), groups)
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error during rollback: %w", err)
	}

	log.Printf("Rollback completed: %s - Processed: %d, Errors: %d", m.Name(), processedCount, errorCount)
	
	if errorCount > 0 {
		return fmt.Errorf("rollback completed with %d errors", errorCount)
	}

	return nil
}
